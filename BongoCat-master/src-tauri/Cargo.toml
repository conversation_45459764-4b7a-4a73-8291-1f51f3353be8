[package]
name = "bongo-cat"
version = "0.6.1"
description = "A Tauri App"
authors = [ "ayangweb" ]
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "bongo_cat_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { workspace = true, features = ["tray-icon", "protocol-asset", "macos-private-api", "image-png"] }
serde = { workspace = true, features = ["derive"] }
serde_json.workspace = true
tauri-plugin-custom-window.workspace = true
rdev = { git = "https://github.com/ayangweb/rdev" }
tauri-plugin-os = "2"
tauri-plugin-process = "2"
tauri-plugin-opener = "2"
tauri-plugin-pinia = "3"
tauri-plugin-log = "2"
tauri-plugin-updater = "2"
tauri-plugin-prevent-default = "1"
tauri-plugin-single-instance = "2"
tauri-plugin-autostart = "2"
tauri-plugin-macos-permissions = "2"
tauri-plugin-dialog = "2"
tauri-plugin-fs = "2"
fs_extra = "1"
tauri-plugin-clipboard-manager = "2"
tauri-plugin-global-shortcut = "2"

[target."cfg(target_os = \"macos\")".dependencies]
tauri-nspanel.workspace = true

[features]
cargo-clippy = []
