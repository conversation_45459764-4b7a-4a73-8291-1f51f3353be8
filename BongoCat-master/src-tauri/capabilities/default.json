{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["*"], "permissions": ["core:default", "core:window:allow-start-dragging", "core:window:allow-set-size", "core:window:deny-internal-toggle-maximize", "core:window:allow-set-always-on-top", "core:window:allow-set-ignore-cursor-events", "core:window:allow-set-decorations", "core:window:allow-set-position", "custom-window:default", "os:default", "process:default", "opener:default", {"identifier": "opener:allow-open-path", "allow": [{"path": "**/*"}]}, "pinia:default", "log:default", "updater:default", "prevent-default:default", "autostart:default", "macos-permissions:default", "dialog:default", "fs:default", "fs:read-all", "fs:write-all", {"identifier": "fs:scope", "allow": ["**/*"]}, "clipboard-manager:allow-write-text", "global-shortcut:allow-is-registered", "global-shortcut:allow-register", "global-shortcut:allow-unregister"]}