{"name": "bongo-cat", "type": "module", "version": "0.6.1", "private": true, "author": {"name": "ayangweb", "email": "<EMAIL>"}, "scripts": {"dev": "run-s build:icon dev:vite", "build": "run-s build:*", "dev:vite": "vite", "build:vite": "vite build", "build:icon": "tsx scripts/buildIcon.ts", "preview": "vite preview", "tauri": "tauri", "lint": "eslint --fix src", "preinstall": "npx only-allow pnpm", "prepare": "simple-git-hooks", "release": "release-it"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-autostart": "~2.3.0", "@tauri-apps/plugin-clipboard-manager": "~2.2.2", "@tauri-apps/plugin-dialog": "~2.2.2", "@tauri-apps/plugin-fs": "~2.3.0", "@tauri-apps/plugin-global-shortcut": "~2.2.1", "@tauri-apps/plugin-log": "~2.3.1", "@tauri-apps/plugin-opener": "~2.2.7", "@tauri-apps/plugin-os": "^2.2.1", "@tauri-apps/plugin-process": "^2.2.1", "@tauri-apps/plugin-updater": "~2.7.1", "@tauri-store/pinia": "^3.7.0", "@vueuse/core": "^13.3.0", "ant-design-vue": "^4.2.6", "dayjs": "^1.11.13", "es-toolkit": "^1.38.0", "is-url": "^1.2.4", "nanoid": "^5.1.5", "pinia": "^3.0.3", "pixi-live2d-display": "^0.4.0", "pixi.js": "^6.5.10", "tauri-plugin-macos-permissions-api": "^2.3.0", "vue": "^3.5.16", "vue-markdown-render": "^2.2.1", "vue-router": "^4.5.1", "vue3-masonry-css": "^1.0.7"}, "devDependencies": {"@antfu/eslint-config": "^4.13.3", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@iconify-json/iconamoon": "^1.2.2", "@iconify-json/solar": "^1.2.2", "@tauri-apps/cli": "^2.5.0", "@types/is-url": "^1.2.32", "@types/node": "^22.15.29", "@unocss/eslint-plugin": "^66.1.3", "@vitejs/plugin-vue": "^5.2.4", "eslint": "^9.28.0", "eslint-plugin-format": "^1.0.1", "lint-staged": "^15.5.2", "npm-run-all": "^4.1.5", "release-it": "^18.1.2", "sass": "^1.89.1", "simple-git-hooks": "^2.13.0", "tsx": "^4.19.4", "typescript": "~5.6.3", "unocss": "66.1.0-beta.7", "vite": "^6.3.5"}, "simple-git-hooks": {"commit-msg": "npx --no-install commitlint -e", "pre-commit": "npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}