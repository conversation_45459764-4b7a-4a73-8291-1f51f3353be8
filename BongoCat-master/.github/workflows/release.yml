name: BongoCat Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  create-release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set output
        id: vars
        run: echo "tag=${GITHUB_REF#refs/*/}" >> $GITHUB_OUTPUT

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Generate changelog
        id: create_release
        run: npx changelogithub --draft --name ${{ steps.vars.outputs.tag }}
        env:
          GITHUB_TOKEN: ${{ secrets.RELEASE_TOKEN }}

  build-app:
    needs: create-release
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: macos-latest
            target: aarch64-apple-darwin
          - platform: macos-latest
            target: x86_64-apple-darwin

          - platform: windows-latest
            target: x86_64-pc-windows-msvc
          - platform: windows-latest
            target: i686-pc-windows-msvc
          - platform: windows-latest
            target: aarch64-pc-windows-msvc

          - platform: ubuntu-22.04
            target: x86_64-unknown-linux-gnu
          - platform: ubuntu-22.04-arm
            target: aarch64-unknown-linux-gnu

    runs-on: ${{ matrix.platform }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - uses: pnpm/action-setup@v3
        with:
          version: latest

      - name: Install rust target
        run: rustup target add ${{ matrix.target }}

      - name: Install dependencies (ubuntu only)
        if: startsWith(matrix.platform, 'ubuntu')
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf xdg-utils

      - name: Install Rust stable
        uses: dtolnay/rust-toolchain@stable

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          workspaces: target

      - name: Sync node version and setup cache
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: pnpm

      - name: Install front-end dependencies
        run: pnpm install --frozen-lockfile

      - name: Build the app
        uses: tauri-apps/tauri-action@v0
        env:
          CI: false
          PLATFORM: ${{ matrix.platform }}
          GITHUB_TOKEN: ${{ secrets.RELEASE_TOKEN }}
          TAURI_SIGNING_PRIVATE_KEY: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY }}
          TAURI_SIGNING_PRIVATE_KEY_PASSWORD: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY_PASSWORD }}
        with:
          tagName: ${{ github.ref_name }}
          releaseName: BongoCat ${{ needs.create-release.outputs.APP_VERSION }}
          releaseBody: ''
          releaseDraft: true
          prerelease: false
          args: --target ${{ matrix.target }}
