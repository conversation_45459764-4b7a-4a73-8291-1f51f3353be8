[workspace]
resolver = "2"
members = [ "src-tauri" ]

[profile.release]
strip = true
opt-level = 3
codegen-units = 1
panic = "abort"
debug-assertions = false
overflow-checks = false
lto = true

[workspace.dependencies]
tauri = "2"
serde = "1"
serde_json = "1"
fs_extra = "1"
tauri-plugin = { version = "2", features = [ "build" ] }
tauri-nspanel = { git = "https://github.com/ahkohd/tauri-nspanel", branch = "v2" }
tauri-plugin-custom-window = { path = "./src-tauri/src/plugins/window" }
